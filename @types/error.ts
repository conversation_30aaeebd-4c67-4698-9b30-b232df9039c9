export class ParamsError extends Error {
	// 400：请求参数不足/不合规
	public readonly statusCode: number = 400;

	constructor(message: string) {
		super(message);
		this.name = "ParamsError";
	}
}
export class AuthError extends Error {
	// 401：未登录/未授权
	public readonly statusCode: number = 401;

	constructor(message: string) {
		super(message);
		this.name = "AuthError";
	}
}
export class Credits402Error extends Error {
	// 402：剩余token数量不足
	public readonly statusCode: number = 402;

	constructor(message: string) {
		super(message);
		this.name = "Credits402Error";
	}
}
export class NotFoundError extends Error {
	// 404：未找到
	public readonly statusCode: number = 404;

	constructor(message: string) {
		super(message);
		this.name = "NotFoundError";
	}
}
export class MethodNotImplementedError extends Error {
	// 405: 服务器在fetch时遇到了未预料的情况
	public readonly statusCode: number = 405;

	constructor(message: string) {
		super(message);
		this.name = "MethodNotImplementedError";
	}
}
export class UnprocessableError extends Error {
	// 422：请求参数不正确
	public readonly statusCode: number = 422;

	constructor(message: string) {
		super(message);
		this.name = "UnprocessableError";
	}
}
export class ServerError extends Error {
	// 500：服务器在处理请求时遇到了未预料的情况
	public readonly statusCode: number = 500;

	constructor(message: string) {
		super(message);
		this.name = "ServerError";
	}
}
export class NotImplementedError extends Error {
	// 501：服务器不支持当前请求所需要的某个功能
	public readonly statusCode: number = 501;

	constructor(message: string) {
		super(message);
		this.name = "NOT_IMPLEMENTED";
	}
}

export class IgnoreError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "IgnoreError";
	}
}

export const handleError = (status: number | undefined | null, message: string | undefined | null) => {
	if (!status) return;

	if (status === 200) return;

	switch (status) {
		case 400:
			throw new ParamsError(message as string);
		case 401:
			throw new AuthError(message as string);
		case 402:
			throw new Credits402Error(message as string);
		case 404:
			throw new NotFoundError(message as string);
		case 405:
			throw new MethodNotImplementedError(message as string);
		case 422:
			throw new UnprocessableError(message as string);
		case 500:
			throw new ServerError(message as string);
		case 501:
			throw new NotImplementedError(message as string);
		default:
			throw new Error(message as string);
	}
};

// tRPC 错误处理函数
export const handleTRPCErrorTransform = (error: any): Error => {
	// 检查是否是 tRPC 错误
	if (error?.data?.code) {
		const code = error.data.code;
		const message = error.message || "Unknown error";

		switch (code) {
			case "BAD_REQUEST":
				return new ParamsError(message);
			case "UNAUTHORIZED":
				return new AuthError(message);
			case "PAYMENT_REQUIRED":
				return new Credits402Error(message);
			case "NOT_FOUND":
				return new NotFoundError(message);
			case "METHOD_NOT_IMPLEMENTED":
				return new MethodNotImplementedError(message);
			case "UNPROCESSABLE_CONTENT":
				return new UnprocessableError(message);
			case "INTERNAL_SERVER_ERROR":
				return new ServerError(message);
			case "NOT_IMPLEMENTED":
				return new NotImplementedError(message);
			default:
				return error;
		}
	}

	return error;
};
