import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { saveToR2 } from "@/server/r2.server";
import { constructErrorFalAI, handleApiError } from "@/@types/error-api";
import { genGemini2_5FromFal } from "@/server/ai/gemini.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE } from "@/lib/track-events";

type Params = {
	prompt: string;
	images: string[];
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.prompt || !params.images) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const needCredits = 5;
		const { isValid, creditConsumes, membershipLevel } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});
		if (!isValid) {
			return NextResponse.json({ status: 402, message: "You have not enough credits." });
		}

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_EDIT_IMAGE, userId, {
			mp_country_code: cfIpCountryCode,
			ip: cfIp,
			membershipLevel: membershipLevel,
		});

		const resultUrls: string[] = await genGemini2_5FromFal(params.prompt, 1, params.images);
		// const resultUrl = resultUrls[0];

		// save to r2
		// const imagePath = await saveToR2(resultUrl);
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const db = getDB();
		const imageResultId = getUUIDString();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					status: MediaResultStatus.Completed,
					type: MediaHeadType.Image,
					tool: MediaHeadToolType.ImageEditor,
					visibility: false,
					prompt: params.prompt,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();

			await tx.insert(mediaItemSchema).values(
				imagePaths.map((imagePath, index) => ({
					uid: getUUIDString(),
					userId: userId,
					mediaHeadUid: imageResultId,
					visibility: false,
					mediaPath: imagePath,
				})),
			);
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image result head uid: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: imagePaths.map((path) => `${OSS_URL_HOST}/${path}`) });
	} catch (error: any) {
		const finalError = constructErrorFalAI(error);
		return handleApiError(finalError, `${WEBNAME} - /api/v1/image/edit-image`);
	}
}
