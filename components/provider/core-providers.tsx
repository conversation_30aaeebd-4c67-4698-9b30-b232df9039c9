"use client";

import { <PERSON>actN<PERSON>, useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { getUrl, TRPCProvider } from "@/trpc/client";
import { httpBatchLink, splitLink, httpSubscriptionLink, createTR<PERSON>Client } from "@trpc/client";
import superjson from "superjson";
import { makeQueryClient } from "@/trpc/query-client";
import { AppRouter } from "@/server/trpc/routers/_app";
import { Toaster } from "sonner";
import { InitializeUser } from "../shared/initialize-user";
import SignInDialog from "../shared/sigin-in-dialog";
import PlanDialog from "../shared/plan-dialog";
import { AnalyticsClarity } from "../analytics/analytics-clarity";
import { AnalyticsGoogle } from "../analytics/analytics-google";
// import { CookiesProvider } from "next-client-cookies/server";
// import { ThemeProvider } from "next-themes";

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
	if (typeof window === "undefined") {
		return makeQueryClient();
	} else {
		if (!browserQueryClient) browserQueryClient = makeQueryClient();
		return browserQueryClient;
	}
}

export function CoreProviders({ children }: { children: ReactNode }) {
	const queryClient = getQueryClient();

	const [trpcClient] = useState(() =>
		createTRPCClient<AppRouter>({
			links: [
				splitLink({
					condition: (op) => op.type === "subscription",
					true: httpSubscriptionLink({
						transformer: superjson,
						url: getUrl(),
					}),
					false: httpBatchLink({
						transformer: superjson,
						url: getUrl(),
						headers() {
							return {
								"x-trpc-source": "client",
							};
						},
					}),
				}),
			],
		}),
	);

	return (
		<QueryClientProvider client={queryClient}>
			<TRPCProvider trpcClient={trpcClient} queryClient={queryClient}>
				{/* <CookiesProvider> */}
				{/* <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark"> */}
				{children}
				<Toaster richColors position="top-center" />
				<InitializeUser />
				<SignInDialog />
				<PlanDialog />
				<AnalyticsClarity />
				<AnalyticsGoogle />
				{/* </ThemeProvider> */}
				{/* </CookiesProvider> */}
			</TRPCProvider>
		</QueryClientProvider>
	);
}
