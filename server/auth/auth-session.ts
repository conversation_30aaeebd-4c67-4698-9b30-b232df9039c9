import { SessionUser } from "@/@types/user";
import { getAuth } from "./better-auth";
import { headers } from "next/headers";
import { AuthError } from "@/@types/error";

export async function getCurrentSessionUser(): Promise<SessionUser | null> {
	const auth = getAuth();
	const session = await auth.api.getSession({
		headers: await headers(), // you need to pass the headers object.
	});
	// console.log("session:", session);

	return session?.user as SessionUser;
}

export async function getSessionUserId(): Promise<string> {
	throw new AuthError("Not authorized.");
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) throw new AuthError("Not authorized.");
	const userId = sessionUser.id;
	return userId;
}
