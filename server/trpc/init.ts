import { initTRPC } from "@trpc/server";
import superjson from "superjson";
import { ZodError } from "zod";
import type { Context } from "./context";
import { TRPCError } from "@trpc/server";

const t = initTRPC.context<Context>().create({
	transformer: superjson,
	errorFormatter({ shape, error }) {
		return {
			...shape,
			data: {
				...shape.data,
				zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,
			},
		};
	},
});

// limiting middleware
const limitMiddleware = t.middleware(async ({ ctx, next }) => {
	const req = ctx.req;
	if (!req) {
		console.log("[DEBUG] No request object in context, skipping rate limit");
		return next();
	}

	// Extract IP from request
	const ip = req.headers.get?.("x-forwarded-for") || req.headers.get?.("x-real-ip") || "unknown";

	console.log(`[DEBUG] tRPC middleware check for IP: ${ip}`);

	// if () {
	// 	console.log(`[DEBUG] tRPC request blocked`);
	// 	throw new TRPCError({
	// 		code: "",
	// 		message: ``,
	// 	});
	// }

	console.log(`[DEBUG] tRPC request allowed`);
	return next();
});

export const router = t.router;
export const publicProcedure = t.procedure;
export const limitedProcedure = t.procedure.use(limitMiddleware);

export const createCallerFactory = t.createCallerFactory;
