import { z } from "zod";
import { publicProcedure, router } from "../init";
import { userRouter } from "./user";
import { imageRouter } from "./image";

export const appRouter = router({
	user: userRouter,
	image: imageRouter,
	generateTextToImage: publicProcedure
		.input(
			z.object({
				prompt: z.string(),
				seed: z.number().optional(),
				imageSize: z.enum(["landscape_4_3", "portrait_4_3", "square", "landscape_16_9", "portrait_16_9"]).optional(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			try {
				return {
					url: "",
				};
			} catch (error) {
				console.error("Error in text-to-image generation:", error);
				throw new Error(error instanceof Error ? error.message : "Failed to generate image");
			}
		}),
});

export type AppRouter = typeof appRouter;
