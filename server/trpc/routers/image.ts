import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { publicProcedure, router } from "../init";
import { getSessionUserId } from "@/server/auth/auth-session";
import { getUUIDString } from "@/lib/utils";
import { saveToR2 } from "@/server/r2.server";
import { OSS_URL_HOST } from "@/lib/constants";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { genGemini2_5FromFal } from "@/server/ai/gemini.server";
import { getDB } from "@/server/db/db-client.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE_WITH_TOOL } from "@/lib/track-events";

export const imageRouter = router({
	// 图片生成/编辑
	removeObject: publicProcedure
		.input(
			z.object({
				prompt: z.string().min(1, "Prompt is required"),
				image: z.string().url("Invalid image URL"),
				tool: z.string().optional().default("image-editor"),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			try {
				// 验证用户身份
				const userId = await getSessionUserId();
				if (!userId) {
					throw new TRPCError({
						code: "UNAUTHORIZED",
						message: "Not authorized.",
					});
				}

				// 检查用户积分
				const needCredits = 5;
				const { isValid, creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});
				if (!isValid) {
					throw new TRPCError({
						code: "PAYMENT_REQUIRED",
						message: "You have not enough credits.",
					});
				}

				console.log("generateImage params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 获取请求信息用于追踪
				const req = ctx.req;
				const cfIpCountryCode = req?.headers.get?.("cf-ipcountry") || null;
				const cfIp = req?.headers.get?.("cf-connecting-ip") || null;

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE_WITH_TOOL, userId, {
					mp_country_code: cfIpCountryCode,
					ip: cfIp,
					membershipLevel: membershipLevel,
					tool: input.tool,
				});

				// 调用 AI 服务生成图片
				const resultUrls: string[] = await genGemini2_5FromFal(input.prompt, 1, [input.image]);

				// 保存到 R2
				const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					const [media]: MediaHead[] = await tx
						.insert(mediaHeadSchema)
						.values({
							uid: imageResultId,
							userId: userId,
							status: MediaResultStatus.Completed,
							type: MediaHeadType.Image,
							tool: MediaHeadToolType.ImageEditor,
							visibility: false,
							prompt: input.prompt,
							creditsSources: JSON.stringify(creditConsumes),
						})
						.returning();

					await tx.insert(mediaItemSchema).values(
						imagePaths.map((imagePath, index) => ({
							uid: getUUIDString(),
							userId: userId,
							mediaHeadUid: imageResultId,
							visibility: false,
							mediaPath: imagePath,
						})),
					);
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image generation result head uid: ${imageResultId}.`,
				});

				// 返回结果
				const resultUrl = `${OSS_URL_HOST}/${imagePaths[0]}`;
				return {
					resultUrl: resultUrl,
					imageResultId: imageResultId,
				};
			} catch (error) {
				console.error("Error in generateImage:", error);

				// 处理特定错误类型
				if (error instanceof TRPCError) {
					throw error;
				}

				// 处理其他错误
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: error instanceof Error ? error.message : "Failed to generate image",
				});
			}
		}),
});
