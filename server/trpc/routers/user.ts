import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { publicProcedure, router } from "../init";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { UserInfoDB } from "@/@types/user";
import { refreshUser } from "@/server/refresh-user";

export const userRouter = router({
	// 查询：根据 ID 获取用户
	getUserById: publicProcedure.query(async () => {
		const sessionUser = await getCurrentSessionUser();
		if (!sessionUser) {
			throw new TRPCError({
				code: "UNAUTHORIZED",
				message: "Not authorized.",
			});
			// return NextResponse.json({ status: 401, message: "Not authorized." });
		}
		const userId = sessionUser.id;

		const userInfo: UserInfoDB | null = await refreshUser(userId);

		return userInfo;
	}),
});
