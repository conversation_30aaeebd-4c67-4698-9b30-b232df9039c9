"use client";

import { createTRPCContext } from "@trpc/tanstack-react-query";
import type { AppRouter } from "@/server/trpc/routers/_app";
import { WEBHOST } from "@/lib/constants";

export const { TRPCProvider, useTRPC, useTRPCClient } = createTRPCContext<AppRouter>();

export function getUrl() {
	const base = (() => {
		if (typeof window !== "undefined") return "";
		return WEBHOST;
	})();

	return `${base}/api/trpc`;
}
